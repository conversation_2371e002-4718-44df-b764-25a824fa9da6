import json
import os
import glob
import argparse
from datetime import datetime
from pathlib import Path

try:
    from openpyxl import Workbook
    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("Warning: openpyxl not available. Will create CSV report instead.")

def setup_logging():
    """Setup basic logging for evaluation."""
    import logging
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"evaluation_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )
    
    return logging.getLogger(__name__)

def load_json_file(file_path):
    """Load and parse JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_inference_fields(data):
    """Extract inference result fields from the JSON data."""
    if not data:
        return {}

    # If 'inference_result' exists, use original logic
    if 'inference_result' in data:
        inference_result = data['inference_result']
        extracted_fields = {}
        for key, value in inference_result.items():
            if key == 'SERVICES_TABLE':
                if isinstance(value, list) and len(value) > 0:
                    for i, service in enumerate(value):
                        for service_key, service_value in service.items():
                            field_name = f"SERVICES_TABLE[{i}].{service_key}"
                            extracted_fields[field_name] = service_value
            elif key == 'TAX':
                if isinstance(value, list):
                    for i, tax_value in enumerate(value):
                        field_name = f"TAX[{i}]"
                        extracted_fields[field_name] = tax_value
            else:
                extracted_fields[key] = value
        return extracted_fields
    else:
        # If no 'inference_result', treat top-level keys as fields (flat structure)
        return dict(data)

def extract_confidence_values(data):
    """Extract confidence values from explainability_info."""
    confidence_values = {}

    if not data or 'explainability_info' not in data:
        return confidence_values

    explainability_info = data['explainability_info']
    if not isinstance(explainability_info, list) or len(explainability_info) == 0:
        return confidence_values

    # Get the first explainability info object
    explain_data = explainability_info[0]

    for key, value in explain_data.items():
        if isinstance(value, dict) and 'confidence' in value:
            # Convert confidence to integer percentage
            confidence_values[key] = f"{int(value['confidence'] * 100)}%"
        elif isinstance(value, list):
            # Handle arrays like TAX
            for i, item in enumerate(value):
                if isinstance(item, dict) and 'confidence' in item:
                    field_name = f"{key}[{i}]"
                    confidence_values[field_name] = f"{int(item['confidence'] * 100)}%"

        # Handle SERVICES_TABLE confidence values
        if key == 'SERVICES_TABLE' and isinstance(value, list):
            for i, service in enumerate(value):
                if isinstance(service, dict):
                    for service_key, service_value in service.items():
                        if isinstance(service_value, dict) and 'confidence' in service_value:
                            field_name = f"SERVICES_TABLE[{i}].{service_key}"
                            confidence_values[field_name] = f"{int(service_value['confidence'] * 100)}%"

    return confidence_values

def compare_values(extracted_value, true_value):
    """Compare two values and return True if they match."""
    # Handle None values
    if extracted_value is None and true_value is None:
        return True
    if extracted_value is None or true_value is None:
        return False
    
    # Convert to strings for comparison to handle different types
    extracted_str = str(extracted_value).strip()
    true_str = str(true_value).strip()
    
    # Handle empty strings
    if extracted_str == "" and true_str == "":
        return True
    
    return extracted_str == true_str

def evaluate_single_file(extracted_file, true_file, file_name, logger):
    """Evaluate a single file pair and return comparison results."""
    logger.info(f"Evaluating: {extracted_file} vs {true_file}")

    # Load both files
    extracted_data = load_json_file(extracted_file)
    true_data = load_json_file(true_file)

    if not extracted_data or not true_data:
        logger.error(f"Failed to load data files")
        return None

    # Extract inference fields and confidence values
    extracted_fields = extract_inference_fields(extracted_data)
    true_fields = extract_inference_fields(true_data)
    confidence_values = extract_confidence_values(extracted_data)

    # Map output fields to true data fields
    field_mapping = {
        'invoice_amount': 'TOTAL',
        'invoice_date': 'DATE',
        'invoice_number': 'ID'
    }

    results = []
    for extracted_field, true_field in field_mapping.items():
        extracted_value = extracted_fields.get(extracted_field, "")
        true_value = true_fields.get(true_field, "")
        is_correct = compare_values(extracted_value, true_value)
        confidence = confidence_values.get(extracted_field, "")

        results.append({
            'File Name': file_name,
            'Field Name': f"{extracted_field} <-> {true_field}",
            'True Data Value': true_value,
            'Extracted Value': extracted_value,
            'Confidence': confidence,
            'Correct': "TRUE" if is_correct else "FALSE"
        })

    return results

def create_csv_report(all_results, output_file, logger):
    """Create CSV report as fallback when openpyxl is not available."""
    logger.info(f"Creating CSV report: {output_file}")

    import csv

    # Change extension to .csv
    csv_file = output_file.replace('.xlsx', '.csv')

    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # Write header for combined report
        writer.writerow(['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 'Confidence', 'Correct'])

        # Combine all results
        for file_name, results in all_results.items():
            for result in results:
                writer.writerow([
                    result['File Name'],
                    result['Field Name'],
                    str(result['True Data Value']),
                    str(result['Extracted Value']),
                    str(result['Confidence']),
                    result['Correct']
                ])

        # Write statistics summary
        writer.writerow([])
        writer.writerow(['=== STATISTICS SUMMARY ==='])

        for file_name, results in all_results.items():
            correct_count = sum(1 for r in results if r['Correct'] == "TRUE")
            total_count = len(results)
            accuracy = round((correct_count / total_count * 100) if total_count > 0 else 0, 2)

            writer.writerow([f"{file_name} - Total Fields", total_count])
            writer.writerow([f"{file_name} - Correct", correct_count])
            writer.writerow([f"{file_name} - Wrong", total_count - correct_count])
            writer.writerow([f"{file_name} - Accuracy (%)", accuracy])
            writer.writerow([])

    logger.info(f"CSV report saved to: {csv_file}")

def create_excel_report(all_results, output_file, logger):
    """Create Excel report with formatting."""
    logger.info(f"Creating Excel report: {output_file}")

    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Evaluation Report"

    # Add headers
    headers = ['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 'Confidence', 'Correct']
    ws.append(headers)

    # Style headers
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)

    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # Add data rows from all files
    row_idx = 2
    for file_name, results in all_results.items():
        for result in results:
            ws.cell(row=row_idx, column=1, value=result['File Name'])
            ws.cell(row=row_idx, column=2, value=result['Field Name'])
            ws.cell(row=row_idx, column=3, value=str(result['True Data Value']))
            ws.cell(row=row_idx, column=4, value=str(result['Extracted Value']))
            ws.cell(row=row_idx, column=5, value=str(result['Confidence']))
            ws.cell(row=row_idx, column=6, value=result['Correct'])

            # Apply formatting based on correctness
            if result['Correct'] == "TRUE":
                fill_color = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
            else:
                fill_color = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")

            # Apply fill to the entire row
            for col in range(1, len(headers) + 1):
                ws.cell(row=row_idx, column=col).fill = fill_color

            row_idx += 1

    # Add statistics at the end
    stats_start_row = row_idx + 2

    # Add statistics header
    ws.cell(row=stats_start_row, column=1, value="STATISTICS SUMMARY")
    ws.cell(row=stats_start_row, column=1).font = Font(bold=True, size=14)
    stats_start_row += 1

    # Add statistics for each file
    for file_name, results in all_results.items():
        correct_count = sum(1 for r in results if r['Correct'] == "TRUE")
        total_count = len(results)
        accuracy = round((correct_count / total_count * 100) if total_count > 0 else 0, 2)

        # File name header
        ws.cell(row=stats_start_row, column=1, value=f"{file_name}:")
        ws.cell(row=stats_start_row, column=1).font = Font(bold=True)
        stats_start_row += 1

        # Statistics data
        stats = [
            ("Total Fields", total_count),
            ("Correct", correct_count),
            ("Wrong", total_count - correct_count),
            ("Accuracy (%)", accuracy)
        ]

        for stat_name, stat_value in stats:
            ws.cell(row=stats_start_row, column=1, value=f"  {stat_name}")
            ws.cell(row=stats_start_row, column=2, value=stat_value)
            stats_start_row += 1

        stats_start_row += 1  # Add space between files

    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
        ws.column_dimensions[column_letter].width = adjusted_width

    # Save workbook
    wb.save(output_file)
    logger.info(f"Excel report saved to: {output_file}")

def find_file_pairs(extracted_dir, true_dir, logger):
    """Find matching pairs of extracted and true data files."""
    extracted_files = glob.glob(os.path.join(extracted_dir, "*_output_result.json"))
    extracted_files += glob.glob(os.path.join(extracted_dir, "*_extracted.json"))
    file_pairs = []
    
    for extracted_file in extracted_files:
        # Extract base filename
        if extracted_file.endswith("_output_result.json"):
            base_name = os.path.basename(extracted_file).replace("_output_result.json", "")
        elif extracted_file.endswith("_extracted.json"):
            base_name = os.path.basename(extracted_file).replace("_extracted.json", "")
        else:
            base_name = os.path.basename(extracted_file)
        true_file = os.path.join(true_dir, f"{base_name}_true_data.json")
        
        if os.path.exists(true_file):
            file_pairs.append((extracted_file, true_file, base_name))
            logger.info(f"Found pair: {base_name}")
        else:
            logger.warning(f"No true data file found for: {base_name}")
    
    return file_pairs

def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate extracted data against true data')
    parser.add_argument('--extracted-dir', '-e', default='data/output_data/extraction',
                       help='Directory containing extracted result files')
    parser.add_argument('--true-dir', '-t', default='data/true_data/extraction',
                       help='Directory containing true data files')
    parser.add_argument('--output-file', '-o', default='data/evaluation/evaluation_report.xlsx',
                       help='Output Excel file path')

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging()
    logger.info("Starting evaluation process")

    # Ensure output directory exists (create evaluation folder)
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Find file pairs
    file_pairs = find_file_pairs(args.extracted_dir, args.true_dir, logger)

    if not file_pairs:
        logger.error("No matching file pairs found!")
        return

    # Evaluate all files
    all_results = {}

    for extracted_file, true_file, base_name in file_pairs:
        results = evaluate_single_file(extracted_file, true_file, base_name, logger)
        if results:
            all_results[base_name] = results

    if not all_results:
        logger.error("No successful evaluations!")
        return

    # Create report (Excel or CSV depending on availability)
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, args.output_file, logger)
    else:
        create_csv_report(all_results, args.output_file, logger)

    # Print summary
    logger.info("\n=== EVALUATION SUMMARY ===")
    for file_name, results in all_results.items():
        total = len(results)
        correct = sum(1 for r in results if r['Correct'] == "TRUE")
        accuracy = (correct / total * 100) if total > 0 else 0
        logger.info(f"{file_name}: {correct}/{total} correct ({accuracy:.1f}%)")

if __name__ == "__main__":
    main()
